/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  margin: 0;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  padding: 20px;
}

#root {
  width: 100%;
  max-width: 1024px;
}

h1,
h2,
h3 {
  color: #1a5276;
  border-bottom: 2px solid #aed6f1;
  padding-bottom: 5px;
  margin-top: 1.5em;
}

.container {
  background-color: #fff;
  padding: 25px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.upload-section {
  margin-bottom: 30px;
  text-align: center;
  padding: 20px;
  border: 2px dashed #aed6f1;
  border-radius: 8px;
  background-color: #fdfefe;
}

.upload-section p {
  color: #555;
  margin-bottom: 20px;
}

input[type="file"] {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  background-color: #f1f1f1;
  margin-right: 10px;
}

button {
  background-color: #2e86c1;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
  font-weight: bold;
}

button:hover {
  background-color: #21618c;
}

button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

#status {
  margin-top: 15px;
  font-weight: bold;
  font-size: 1.1em;
  min-height: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #eaf2f8;
  box-sizing: border-box;
}

.full-width {
  grid-column: 1 / -1;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  font-size: 0.9em;
  overflow-x: auto;
  display: block;
}

th,
td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
  white-space: nowrap;
}

th {
  background-color: #eaf2f8;
  color: #34495e;
  font-weight: bold;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}
