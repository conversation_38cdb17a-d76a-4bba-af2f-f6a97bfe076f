/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState } from "react";
import { createRoot } from "react-dom/client";
import { GoogleGenAI, Type } from "@google/genai";

const App = () => {
  const [file, setFile] = useState<File | null>(null);
  const [status, setStatus] = useState({ message: "", color: "" });
  const [isLoading, setIsLoading] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);

  const prognosisSchema = {
    type: Type.OBJECT,
    properties: {
      well_name: {
        type: Type.STRING,
        description: "The primary name of the well.",
      },
      rig_name: {
        type: Type.STRING,
        description: "The name of the rig associated with the well.",
      },
      well_type: {
        type: Type.STRING,
        description: "The type of the well (e.g., Exploration, Development).",
      },
      prognosis_status: {
        type: Type.STRING,
        description: "The current status of the prognosis (e.g., Approved).",
      },
      summary_well_information: {
        type: Type.OBJECT,
        description: "A detailed summary of the well's information.",
        properties: {
          field: { type: Type.STRING },
          well_orientation: { type: Type.STRING },
          targeted_zones: { type: Type.STRING },
          job_description: { type: Type.STRING },
          budget_category: { type: Type.STRING },
          completion_form: { type: Type.STRING },
          basic_duration: { type: Type.NUMBER },
          total_duration: { type: Type.NUMBER },
          expected_start_date: { type: Type.STRING },
          release_date: { type: Type.STRING },
          well_owner: { type: Type.STRING },
          cost_estimate: { type: Type.NUMBER },
        },
      },
      development_plan: { type: Type.STRING },
      authorization: { type: Type.STRING },
      potential_rates_prognosis: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            category: { type: Type.STRING },
            potential_rate: { type: Type.STRING },
            technical_rate: { type: Type.STRING },
            water_injection_rate: { type: Type.STRING },
            gas_injection_rate: { type: Type.STRING },
          },
        },
      },
      proposed_location_control_points: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            control_point_type: { type: Type.STRING },
            reservoir_description: { type: Type.STRING },
            coordinates: { type: Type.STRING },
            coordinate_zone: { type: Type.STRING },
            remarks: { type: Type.STRING },
          },
        },
      },
      coring_requirements: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            core_number: { type: Type.INTEGER },
            core_interval_from_to: { type: Type.STRING },
            core_length: { type: Type.STRING },
            preservation: { type: Type.STRING },
            purpose: { type: Type.STRING },
            remarks: { type: Type.STRING },
          },
        },
      },
      open_hole_logging: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            run_number: { type: Type.INTEGER },
            hole_type: { type: Type.STRING },
            interval_from: { type: Type.STRING },
            interval_to: { type: Type.STRING },
            logging_service: { type: Type.STRING },
            techniques: { type: Type.STRING },
            remarks: { type: Type.STRING },
          },
        },
      },
      proposed_perforation: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            reservoir: { type: Type.STRING },
            interval_from_to: { type: Type.STRING },
            footage: { type: Type.NUMBER },
            gun_type: { type: Type.STRING },
            shot_density: { type: Type.STRING },
            purpose: { type: Type.STRING },
            remarks: { type: Type.STRING },
          },
        },
      },
      current_pressure_temperature: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            reservoir: { type: Type.STRING },
            fluid_contents: { type: Type.STRING },
            current_pressure_psig: { type: Type.NUMBER },
            gradient_psi_ft: { type: Type.NUMBER },
            temperature: { type: Type.STRING },
            remarks: { type: Type.STRING },
          },
        },
      },
    },
  };

  const fileToGenerativePart = (file: File) => {
    return new Promise<{ inlineData: { data: string; mimeType: string } }>(
      (resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64Data = (reader.result as string).split(",")[1];
          resolve({
            inlineData: {
              data: base64Data,
              mimeType: file.type,
            },
          });
        };
        reader.onerror = (err) => reject(err);
        reader.readAsDataURL(file);
      }
    );
  };

  const handleExtract = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      setStatus({ message: "Please select a file.", color: "red" });
      return;
    }

    setIsLoading(true);
    setExtractedData(null);
    setStatus({
      message: "Uploading and analyzing document... This may take a moment.",
      color: "#e67e22",
    });

    try {
      const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });
      const filePart = await fileToGenerativePart(file);

      const prompt = `
        Thoroughly analyze the entire provided document, which is a well prognosis report.
        Extract all information and structure it as a single, nested JSON object.
        The JSON object must strictly adhere to the provided schema. Do not invent data or add fields that are not in the schema.
        If a table or field is not found in the document, its value should be null.
      `;

      const response = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: [
          {
            parts: [
              { text: prompt },
              filePart,
            ],
          },
        ],
        config: {
          responseMimeType: "application/json",
          responseSchema: prognosisSchema,
        },
      });

      const data = JSON.parse(response.text.trim());
      setExtractedData(data);
      setStatus({ message: "Extraction successful!", color: "green" });
    } catch (error) {
      console.error(error);
      setStatus({
        message: `Error: ${(error as Error).message}`,
        color: "red",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="upload-section">
        <h1>AI Prognosis Document Extractor</h1>
        <p>
          Upload a Prognosis PDF or Excel file. The AI will read the document
          and populate the form below.
        </p>
        <form id="uploadForm" onSubmit={handleExtract}>
          <input
            type="file"
            id="fileInput"
            name="file"
            accept=".pdf,.xlsx,.xls"
            onChange={(e) => setFile(e.target.files?.[0] || null)}
            required
          />
          <button type="submit" id="submitBtn" disabled={isLoading}>
            {isLoading ? "Processing..." : "Extract Data"}
          </button>
        </form>
        <div id="status" style={{ color: status.color }}>
          {status.message}
        </div>
      </div>

      {extractedData && (
        <form id="resultsForm">
          <h2>🧾 Basic Information</h2>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="well_name">Well Name</label>
              <input
                type="text"
                id="well_name"
                value={extractedData.well_name || ""}
                readOnly
              />
            </div>
            <div className="form-group">
              <label htmlFor="rig_name">Rig Name</label>
              <input
                type="text"
                id="rig_name"
                value={extractedData.rig_name || ""}
                readOnly
              />
            </div>
            <div className="form-group">
              <label htmlFor="well_type">Well Type</label>
              <input
                type="text"
                id="well_type"
                value={extractedData.well_type || ""}
                readOnly
              />
            </div>
            <div className="form-group">
              <label htmlFor="prognosis_status">Prognosis Status</label>
              <input
                type="text"
                id="prognosis_status"
                value={extractedData.prognosis_status || ""}
                readOnly
              />
            </div>
          </div>

          <h2>📊 Summary Well Information</h2>
          <div className="form-grid">
            <div className="form-group">
              <label>Field</label>
              <input
                type="text"
                value={
                  extractedData.summary_well_information?.field || ""
                }
                readOnly
              />
            </div>
            <div className="form-group">
              <label>Well Orientation</label>
              <input
                type="text"
                value={
                  extractedData.summary_well_information?.well_orientation || ""
                }
                readOnly
              />
            </div>
            <div className="form-group">
              <label>Targeted Zones</label>
              <input
                type="text"
                value={
                  extractedData.summary_well_information?.targeted_zones ||
                  ""
                }
                readOnly
              />
            </div>
            <div className="form-group">
              <label>Expected Start Date</label>
              <input
                type="text"
                value={
                  extractedData.summary_well_information?.expected_start_date ||
                  ""
                }
                readOnly
              />
            </div>
            <div className="form-group">
              <label>Release Date</label>
              <input
                type="text"
                value={
                  extractedData.summary_well_information?.release_date || ""
                }
                readOnly
              />
            </div>
            <div className="form-group">
              <label>Well Owner</label>
              <input
                type="text"
                value={
                  extractedData.summary_well_information?.well_owner || ""
                }
                readOnly
              />
            </div>
            <div className="form-group">
              <label>Cost Estimate</label>
              <input
                type="text"
                value={
                  extractedData.summary_well_information?.cost_estimate || ""
                }
                readOnly
              />
            </div>
          </div>

          <h2>🗺 Planning Overview</h2>
          <div className="form-group full-width">
            <label>Development Plan</label>
            <textarea
              value={extractedData.development_plan || ""}
              rows={3}
              readOnly
            ></textarea>
          </div>
          <div className="form-group full-width">
            <label>Authorization</label>
            <textarea
              value={extractedData.authorization || ""}
              rows={2}
              readOnly
            ></textarea>
          </div>

          <h3>Potential Rates (Prognosis)</h3>
          <table>
            <thead>
              <tr>
                <th>Category</th>
                <th>Potential Rate</th>
                <th>Technical Rate</th>
                <th>Water Injection Rate</th>
                <th>Gas Injection Rate</th>
              </tr>
            </thead>
            <tbody>
              {extractedData.potential_rates_prognosis?.map((item, i) => (
                <tr key={i}>
                  <td>{item.category || ""}</td>
                  <td>{item.potential_rate || ""}</td>
                  <td>{item.technical_rate || ""}</td>
                  <td>{item.water_injection_rate || ""}</td>
                  <td>{item.gas_injection_rate || ""}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <h3>Proposed Location Control Points</h3>
          <table>
            <thead>
              <tr>
                <th>Type</th>
                <th>Reservoir</th>
                <th>Coordinates</th>
                <th>Zone</th>
                <th>Remarks</th>
              </tr>
            </thead>
            <tbody>
              {extractedData.proposed_location_control_points?.map(
                (item, i) => (
                  <tr key={i}>
                    <td>{item.control_point_type || ""}</td>
                    <td>{item.reservoir_description || ""}</td>
                    <td>{item.coordinates || ""}</td>
                    <td>{item.coordinate_zone || ""}</td>
                    <td>{item.remarks || ""}</td>
                  </tr>
                )
              )}
            </tbody>
          </table>

          <h2>🛠 Well Requirements</h2>
          <h3>Coring Requirements</h3>
          <table>
            <thead>
              <tr>
                <th>Core #</th>
                <th>Interval</th>
                <th>Length</th>
                <th>Preservation</th>
                <th>Purpose</th>
                <th>Remarks</th>
              </tr>
            </thead>
            <tbody>
              {extractedData.coring_requirements?.map((item, i) => (
                <tr key={i}>
                  <td>{item.core_number || ""}</td>
                  <td>{item.core_interval_from_to || ""}</td>
                  <td>{item.core_length || ""}</td>
                  <td>{item.preservation || ""}</td>
                  <td>{item.purpose || ""}</td>
                  <td>{item.remarks || ""}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <h3>Open Hole Logging</h3>
          <table>
            <thead>
              <tr>
                <th>Run #</th>
                <th>Hole Type</th>
                <th>Interval From</th>
                <th>Interval To</th>
                <th>Service</th>
                <th>Techniques</th>
                <th>Remarks</th>
              </tr>
            </thead>
            <tbody>
              {extractedData.open_hole_logging?.map((item, i) => (
                <tr key={i}>
                  <td>{item.run_number || ""}</td>
                  <td>{item.hole_type || ""}</td>
                  <td>{item.interval_from || ""}</td>
                  <td>{item.interval_to || ""}</td>
                  <td>{item.logging_service || ""}</td>
                  <td>{item.techniques || ""}</td>
                  <td>{item.remarks || ""}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <h2>🔬 Reservoir Engineering Data</h2>
          <h3>Proposed Perforation</h3>
          <table>
            <thead>
              <tr>
                <th>Reservoir</th>
                <th>Interval</th>
                <th>Footage</th>
                <th>Gun Type</th>
                <th>Shot Density</th>
                <th>Purpose</th>
                <th>Remarks</th>
              </tr>
            </thead>
            <tbody>
              {extractedData.proposed_perforation?.map((item, i) => (
                <tr key={i}>
                  <td>{item.reservoir || ""}</td>
                  <td>{item.interval_from_to || ""}</td>
                  <td>{item.footage || ""}</td>
                  <td>{item.gun_type || ""}</td>
                  <td>{item.shot_density || ""}</td>
                  <td>{item.purpose || ""}</td>
                  <td>{item.remarks || ""}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <h3>Current Pressure/Temperature</h3>
          <table>
            <thead>
              <tr>
                <th>Reservoir</th>
                <th>Fluid Contents</th>
                <th>Pressure (PSIG)</th>
                <th>Gradient (PSI/ft)</th>
                <th>Temperature</th>
                <th>Remarks</th>
              </tr>
            </thead>
            <tbody>
              {extractedData.current_pressure_temperature?.map((item, i) => (
                <tr key={i}>
                  <td>{item.reservoir || ""}</td>
                  <td>{item.fluid_contents || ""}</td>
                  <td>{item.current_pressure_psig || ""}</td>
                  <td>{item.gradient_psi_ft || ""}</td>
                  <td>{item.temperature || ""}</td>
                  <td>{item.remarks || ""}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </form>
      )}
    </div>
  );
};

const root = createRoot(document.getElementById("root")!);
root.render(<App />);
